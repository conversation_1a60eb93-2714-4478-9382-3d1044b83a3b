use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::time::{Instant, Duration};
use std::collections::HashMap;
use std::sync::Arc;
use dashmap::DashMap;
use std::hash::{Hash, Has<PERSON>};
use std::collections::hash_map::DefaultHasher;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct GrammarCheckResult {
    pub original_text: String,
    pub corrected_text: String,
    pub errors: Vec<GrammarError>,
    pub processing_time: f64,
    pub error_count: usize,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GrammarError {
    pub message: String,
    pub rule_id: String,
    pub category: String,
    pub offset: usize,
    pub length: usize,
    pub context: String,
    pub suggestions: Vec<String>,
    pub severity: String,
    pub confidence: f32,
    pub error_type: ErrorType,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum ErrorType {
    Spelling,
    Grammar,
    Punctuation,
    Style,
    Redundancy,
    Clarity,
    Other,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum GrammarProvider {
    Harper,
    OfflineRules,
    Hybrid,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GrammarConfig {
    pub provider: GrammarProvider,
    pub language: String,
    pub enable_style_checks: bool,
    pub enable_picky_rules: bool,
    pub offline_fallback: bool,
    pub auto_apply_high_confidence: bool,
    pub auto_apply_threshold: f32,
    pub realtime_checking: bool,
    pub smart_suggestions: bool,
}

// Harper-specific structures are handled internally by harper-core
// No need for external API response structures

pub struct GrammarService {
    config: GrammarConfig,
    offline_rules: OfflineGrammarChecker,
    cache: Arc<DashMap<String, (GrammarCheckResult, Instant)>>,
    suggestion_cache: Arc<DashMap<String, Vec<String>>>,
    performance_stats: Arc<DashMap<String, PerformanceMetrics>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    pub total_checks: u64,
    pub average_time_ms: f64,
    pub cache_hits: u64,
    pub cache_misses: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BatchGrammarResult {
    pub results: Vec<GrammarCheckResult>,
    pub total_processing_time: f64,
    pub batch_size: usize,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LanguageStats {
    pub words: usize,
    pub characters: usize,
    pub characters_no_spaces: usize,
    pub sentences: usize,
    pub paragraphs: usize,
    pub reading_time_minutes: usize,
}

impl Default for GrammarConfig {
    fn default() -> Self {
        Self {
            provider: GrammarProvider::Harper, // Use Harper as the primary provider
            language: "en-US".to_string(),
            enable_style_checks: true,
            enable_picky_rules: false,
            offline_fallback: true,
            auto_apply_high_confidence: true,
            auto_apply_threshold: 0.9,
            realtime_checking: true,
            smart_suggestions: true,
        }
    }
}

impl GrammarService {
    pub fn new() -> Self {
        Self::with_config(GrammarConfig::default())
    }

    pub fn with_config(config: GrammarConfig) -> Self {
        Self {
            offline_rules: OfflineGrammarChecker::new(),
            config,
            cache: Arc::new(DashMap::new()),
            suggestion_cache: Arc::new(DashMap::new()),
            performance_stats: Arc::new(DashMap::new()),
        }
    }

    // Keep compatibility with existing code that expects this method
    pub fn with_custom_server(_server_url: String) -> Self {
        // Harper doesn't use custom servers, so just return default config
        Self::with_config(GrammarConfig::default())
    }

    pub fn with_harper_config(config: GrammarConfig) -> Self {
        Self::with_config(config)
    }

    pub async fn check_text(&self, text: &str, auto_correct: bool) -> Result<GrammarCheckResult> {
        if text.trim().is_empty() {
            return Ok(GrammarCheckResult {
                original_text: text.to_string(),
                corrected_text: text.to_string(),
                errors: vec![],
                processing_time: 0.0,
                error_count: 0,
            });
        }

        // Generate optimized cache key using hash for better performance
        let cache_key = self.generate_cache_key(text, auto_correct);

        // Check cache first and update performance metrics
        if let Some(entry) = self.cache.get(&cache_key) {
            let (cached_result, cached_time) = entry.value();
            // Cache valid for 5 minutes
            if cached_time.elapsed() < Duration::from_secs(300) {
                self.update_performance_stats("cache_hit", 0.0);
                return Ok(cached_result.clone());
            } else {
                // Remove expired cache entry
                drop(entry);
                self.cache.remove(&cache_key);
            }
        }

        self.update_performance_stats("cache_miss", 0.0);

        let start_time = Instant::now();

        let errors = match self.config.provider {
            GrammarProvider::Harper => {
                self.check_with_harper(text).unwrap_or_else(|e| {
                    log::warn!("Harper failed: {}, falling back to offline", e);
                    if self.config.offline_fallback {
                        self.offline_rules.check_text(text)
                    } else {
                        vec![]
                    }
                })
            }
            GrammarProvider::OfflineRules => {
                self.offline_rules.check_text(text)
            }
            GrammarProvider::Hybrid => {
                let mut all_errors = self.offline_rules.check_text(text);

                // Try to enhance with Harper
                if let Ok(harper_errors) = self.check_with_harper(text) {
                    // Merge errors, avoiding duplicates
                    for harper_error in harper_errors {
                        if !all_errors.iter().any(|e|
                            e.offset == harper_error.offset &&
                            e.length == harper_error.length
                        ) {
                            all_errors.push(harper_error);
                        }
                    }
                }

                all_errors
            }
        };

        // Apply corrections if requested or if smart auto-correction is enabled
        let corrected_text = if auto_correct && !errors.is_empty() {
            self.apply_smart_corrections(text, &errors)
        } else if self.config.auto_apply_high_confidence && !errors.is_empty() {
            self.apply_high_confidence_corrections(text, &errors)
        } else {
            text.to_string()
        };

        let processing_time = start_time.elapsed().as_secs_f64();

        let result = GrammarCheckResult {
            original_text: text.to_string(),
            corrected_text,
            errors: errors.clone(),
            processing_time,
            error_count: errors.len(),
        };

        // Cache the result and update performance metrics
        self.cache.insert(cache_key, (result.clone(), Instant::now()));
        self.update_performance_stats("check_completed", processing_time);

        // Limit cache size
        if self.cache.len() > 100 {
            self.cleanup_cache();
        }

        Ok(result)
    }

    fn cleanup_cache(&self) {
        let now = Instant::now();
        let expired_keys: Vec<String> = self.cache
            .iter()
            .filter_map(|entry| {
                let (key, (_, timestamp)) = entry.pair();
                if now.duration_since(*timestamp) > Duration::from_secs(300) {
                    Some(key.clone())
                } else {
                    None
                }
            })
            .collect();

        for key in expired_keys {
            self.cache.remove(&key);
        }

        // If still too many entries, remove oldest ones
        if self.cache.len() > 50 {
            let mut entries: Vec<_> = self.cache
                .iter()
                .map(|entry| {
                    let (key, (_, timestamp)) = entry.pair();
                    (key.clone(), *timestamp)
                })
                .collect();
            
            entries.sort_by_key(|(_, timestamp)| *timestamp);
            
            for (key, _) in entries.into_iter().take(self.cache.len() - 50) {
                self.cache.remove(&key);
            }
        }
    }

    pub async fn apply_specific_corrections(
        &self,
        text: &str,
        error_indices: &[usize],
    ) -> Result<String> {
        // First get all errors
        let result = self.check_text(text, false).await?;
        
        if error_indices.is_empty() || result.errors.is_empty() {
            return Ok(text.to_string());
        }

        // Filter errors by indices and sort by offset in reverse order
        let mut selected_errors: Vec<&GrammarError> = error_indices
            .iter()
            .filter_map(|&i| result.errors.get(i))
            .collect();
        
        selected_errors.sort_by(|a, b| b.offset.cmp(&a.offset));

        // Apply corrections
        let mut corrected_text = text.to_string();
        for error in selected_errors {
            if let Some(suggestion) = error.suggestions.first() {
                let start = error.offset;
                let end = error.offset + error.length;
                
                if end <= corrected_text.len() {
                    corrected_text.replace_range(start..end, suggestion);
                }
            }
        }

        Ok(corrected_text)
    }

    pub async fn check_batch(&self, texts: Vec<String>, auto_correct: bool) -> Result<BatchGrammarResult> {
        let start_time = Instant::now();
        let mut results = Vec::new();

        // Process texts in parallel for better performance
        for text in texts {
            let result = self.check_text(&text, auto_correct).await?;
            results.push(result);
        }

        let total_processing_time = start_time.elapsed().as_secs_f64();

        Ok(BatchGrammarResult {
            batch_size: results.len(),
            results,
            total_processing_time,
        })
    }

    fn generate_cache_key(&self, text: &str, auto_correct: bool) -> String {
        let mut hasher = DefaultHasher::new();
        text.trim().hash(&mut hasher);
        auto_correct.hash(&mut hasher);
        self.config.smart_suggestions.hash(&mut hasher);
        self.config.enable_style_checks.hash(&mut hasher);
        format!("{:x}", hasher.finish())
    }

    fn update_performance_stats(&self, operation: &str, time_ms: f64) {
        let mut stats = self.performance_stats.entry(operation.to_string())
            .or_insert(PerformanceMetrics {
                total_checks: 0,
                average_time_ms: 0.0,
                cache_hits: 0,
                cache_misses: 0,
            });

        match operation {
            "cache_hit" => stats.cache_hits += 1,
            "cache_miss" => stats.cache_misses += 1,
            "check_completed" => {
                stats.total_checks += 1;
                stats.average_time_ms = (stats.average_time_ms * (stats.total_checks - 1) as f64 + time_ms * 1000.0) / stats.total_checks as f64;
            },
            _ => {}
        }
    }

    pub fn get_performance_stats(&self) -> HashMap<String, PerformanceMetrics> {
        self.performance_stats.iter()
            .map(|entry| (entry.key().clone(), entry.value().clone()))
            .collect()
    }

    pub fn clear_caches(&self) {
        self.cache.clear();
        self.suggestion_cache.clear();
    }

    fn apply_corrections(&self, text: &str, errors: &[GrammarError]) -> String {
        let mut corrected = text.to_string();
        
        // Sort errors by offset in reverse order to avoid position shifts
        let mut sorted_errors = errors.to_vec();
        sorted_errors.sort_by(|a, b| b.offset.cmp(&a.offset));

        for error in sorted_errors {
            if let Some(suggestion) = error.suggestions.first() {
                let start = error.offset;
                let end = error.offset + error.length;
                
                if end <= corrected.len() {
                    corrected.replace_range(start..end, suggestion);
                }
            }
        }

        corrected
    }

    fn apply_smart_corrections(&self, text: &str, errors: &[GrammarError]) -> String {
        let mut corrected = text.to_string();
        
        // Sort errors by confidence and severity
        let mut sorted_errors = errors.to_vec();
        sorted_errors.sort_by(|a, b| {
            // Sort by confidence descending, then by severity priority
            let conf_cmp = b.confidence.partial_cmp(&a.confidence).unwrap_or(std::cmp::Ordering::Equal);
            if conf_cmp == std::cmp::Ordering::Equal {
                self.get_severity_priority(&b.severity).cmp(&self.get_severity_priority(&a.severity))
            } else {
                conf_cmp
            }
        });

        // Then sort by offset in reverse order to avoid position shifts
        sorted_errors.sort_by(|a, b| b.offset.cmp(&a.offset));

        for error in sorted_errors {
            if self.should_auto_apply(&error) {
                if let Some(suggestion) = self.get_best_suggestion(&error) {
                    let start = error.offset;
                    let end = error.offset + error.length;
                    
                    if end <= corrected.len() {
                        corrected.replace_range(start..end, suggestion);
                    }
                }
            }
        }

        corrected
    }

    fn apply_high_confidence_corrections(&self, text: &str, errors: &[GrammarError]) -> String {
        let mut corrected = text.to_string();
        
        // Filter and sort high-confidence errors
        let mut high_conf_errors: Vec<_> = errors.iter()
            .filter(|e| e.confidence >= self.config.auto_apply_threshold && self.is_safe_to_auto_correct(e))
            .collect();
        
        high_conf_errors.sort_by(|a, b| b.offset.cmp(&a.offset));

        for error in high_conf_errors {
            if let Some(suggestion) = self.get_best_suggestion(error) {
                let start = error.offset;
                let end = error.offset + error.length;
                
                if end <= corrected.len() {
                    corrected.replace_range(start..end, suggestion);
                }
            }
        }

        corrected
    }

    fn should_auto_apply(&self, error: &GrammarError) -> bool {
        if !self.config.smart_suggestions {
            return error.confidence >= self.config.auto_apply_threshold;
        }

        // Smart logic: auto-apply for common, safe error types
        match error.error_type {
            ErrorType::Spelling => error.confidence >= 0.8,
            ErrorType::Punctuation => error.confidence >= 0.9,
            ErrorType::Grammar => error.confidence >= 0.95, // More conservative for grammar
            ErrorType::Style => false, // Never auto-apply style changes
            ErrorType::Redundancy => error.confidence >= 0.9,
            ErrorType::Clarity => false, // Subjective, user should decide
            ErrorType::Other => error.confidence >= 0.95,
        }
    }

    fn is_safe_to_auto_correct(&self, error: &GrammarError) -> bool {
        // Define safe categories for auto-correction
        matches!(error.error_type, 
            ErrorType::Spelling | 
            ErrorType::Punctuation | 
            ErrorType::Redundancy
        ) && error.severity == "error"
    }

    fn get_best_suggestion<'a>(&self, error: &'a GrammarError) -> Option<&'a String> {
        if !self.config.smart_suggestions {
            return error.suggestions.first();
        }

        // Smart suggestion selection
        error.suggestions.iter().find(|suggestion| {
            // Prefer shorter, simpler suggestions for high-confidence errors
            if error.confidence >= 0.9 {
                suggestion.len() <= error.length + 10 // Reasonable length limit
            } else {
                true
            }
        })
    }

    fn get_severity_priority(&self, severity: &str) -> u8 {
        match severity {
            "error" => 3,
            "warning" => 2,
            "info" => 1,
            _ => 0,
        }
    }

    fn check_with_harper(&self, text: &str) -> Result<Vec<GrammarError>> {
        use harper_core::{Document, linting::{LintGroup, Linter}, spell::FstDictionary, Dialect};
        use std::sync::Arc;

        // Create a new document from the text with proper Harper configuration
        let document = Document::new_plain_english_curated(text);

        // Create a dictionary for Harper
        let dictionary = Arc::new(FstDictionary::curated());

        // Create a comprehensive lint group with all Harper's built-in linters
        // Use American English dialect as default
        let mut lint_group = LintGroup::new_curated(dictionary, Dialect::American);

        // Use Harper's built-in linting functionality
        let harper_lints = lint_group.lint(&document);

        let mut errors = Vec::new();

        // Convert Harper's Lint objects to our GrammarError format
        for lint in harper_lints {
            let span = lint.span;
            let start_byte = self.calculate_char_offset_to_byte(text, span.start);
            let end_byte = self.calculate_char_offset_to_byte(text, span.end);
            let length = end_byte.saturating_sub(start_byte);

            // Convert Harper's suggestions to our format
            let suggestions: Vec<String> = lint.suggestions.iter()
                .map(|suggestion| {
                    match suggestion {
                        harper_core::linting::Suggestion::ReplaceWith(replacement) => {
                            replacement.iter().collect()
                        }
                        harper_core::linting::Suggestion::Remove => {
                            "".to_string()
                        }
                        harper_core::linting::Suggestion::InsertAfter(insertion) => {
                            insertion.iter().collect()
                        }
                    }
                })
                .collect();

            // Map Harper's LintKind to our ErrorType and severity
            let (error_type, severity, confidence) = self.map_harper_lint_kind(&lint.lint_kind);

            errors.push(GrammarError {
                message: lint.message,
                rule_id: format!("HARPER_{:?}", lint.lint_kind),
                category: self.get_harper_category(&lint.lint_kind),
                offset: start_byte,
                length,
                context: self.extract_context(text, start_byte, length),
                suggestions,
                severity,
                confidence,
                error_type,
            });
        }

        // Add OCR-specific checks that Harper might miss
        errors.extend(self.check_ocr_specific_errors(text)?);

        log::info!("Harper grammar checking completed with {} lints from Harper's built-in rules", errors.len());
        Ok(errors)
    }

    fn map_harper_lint_kind(&self, lint_kind: &harper_core::linting::LintKind) -> (ErrorType, String, f32) {
        use harper_core::linting::LintKind;

        match lint_kind {
            LintKind::Spelling => (ErrorType::Spelling, "error".to_string(), 0.9),
            LintKind::Repetition => (ErrorType::Redundancy, "info".to_string(), 0.8),
            LintKind::Capitalization => (ErrorType::Grammar, "error".to_string(), 0.85),
            LintKind::Punctuation => (ErrorType::Punctuation, "warning".to_string(), 0.8),
            LintKind::Readability => (ErrorType::Clarity, "info".to_string(), 0.6),
            LintKind::Miscellaneous => (ErrorType::Other, "info".to_string(), 0.5),
            // New Harper lint kinds that exist
            LintKind::Agreement => (ErrorType::Grammar, "error".to_string(), 0.9),
            LintKind::BoundaryError => (ErrorType::Other, "warning".to_string(), 0.7),
            LintKind::Eggcorn => (ErrorType::Spelling, "warning".to_string(), 0.8),
            LintKind::Enhancement => (ErrorType::Style, "info".to_string(), 0.6),
            LintKind::Formatting => (ErrorType::Style, "info".to_string(), 0.7),
            LintKind::Redundancy => (ErrorType::Redundancy, "info".to_string(), 0.8),
            LintKind::WordChoice => (ErrorType::Style, "info".to_string(), 0.6),
            // Catch-all for any other lint kinds
            _ => (ErrorType::Other, "info".to_string(), 0.5),
        }
    }

    fn get_harper_category(&self, lint_kind: &harper_core::linting::LintKind) -> String {
        use harper_core::linting::LintKind;

        match lint_kind {
            LintKind::Spelling => "Spelling".to_string(),
            LintKind::Repetition => "Repetition".to_string(),
            LintKind::Capitalization => "Capitalization".to_string(),
            LintKind::Punctuation => "Punctuation".to_string(),
            LintKind::Readability => "Readability".to_string(),
            LintKind::Miscellaneous => "Grammar".to_string(),
            // New Harper lint kinds that exist
            LintKind::Agreement => "Grammar".to_string(),
            LintKind::BoundaryError => "Formatting".to_string(),
            LintKind::Eggcorn => "Spelling".to_string(),
            LintKind::Enhancement => "Style".to_string(),
            LintKind::Formatting => "Formatting".to_string(),
            LintKind::Redundancy => "Redundancy".to_string(),
            LintKind::WordChoice => "Style".to_string(),
            // Catch-all for any other lint kinds
            _ => "Other".to_string(),
        }
    }

    fn calculate_char_offset_to_byte(&self, text: &str, char_index: usize) -> usize {
        // Convert character index to byte offset in the original text
        // This is a simplified approach that works for most cases
        text.char_indices()
            .nth(char_index)
            .map(|(byte_index, _)| byte_index)
            .unwrap_or(text.len())
    }

    // Simplified helper methods for basic grammar suggestions
    fn generate_basic_suggestions(&self, problem_text: &str, rule_type: &str) -> Vec<String> {
        match rule_type {
            "repeated_word" => {
                vec![problem_text.split_whitespace().next().unwrap_or(problem_text).to_string()]
            },
            "capitalization" => {
                vec![self.capitalize_first_char(problem_text)]
            },
            "long_sentence" => {
                vec!["Consider breaking this sentence into shorter ones.".to_string()]
            },
            _ => {
                vec!["Review this text for potential improvements.".to_string()]
            }
        }
    }

    fn extract_context(&self, text: &str, offset: usize, length: usize) -> String {
        let context_size = 50;
        let start = offset.saturating_sub(context_size);
        let end = (offset + length + context_size).min(text.len());

        text.chars()
            .skip(start)
            .take(end - start)
            .collect()
    }

    fn get_spelling_suggestions(&self, word: &str) -> Vec<String> {
        let mut suggestions = Vec::new();

        // First check our offline common errors dictionary
        if let Some(correction) = self.offline_rules.common_errors.get(&word.to_lowercase()) {
            suggestions.push(correction.clone());
        }

        // Add OCR-specific corrections for common character misrecognitions
        suggestions.extend(self.get_ocr_specific_corrections(word));

        // Generate basic spelling suggestions using simple heuristics
        suggestions.extend(self.generate_basic_spelling_suggestions(word));

        // If no suggestions found, add the original word
        if suggestions.is_empty() {
            suggestions.push(word.to_string());
        }

        // Remove duplicates and limit total suggestions
        suggestions.sort();
        suggestions.dedup();
        suggestions.truncate(5);
        suggestions
    }

    fn generate_basic_spelling_suggestions(&self, word: &str) -> Vec<String> {
        let mut suggestions = Vec::new();

        // Check for common letter substitutions
        let substitutions = [
            ('a', 'e'), ('e', 'a'), ('i', 'e'), ('e', 'i'), ('o', 'u'), ('u', 'o'),
            ('c', 'k'), ('k', 'c'), ('s', 'z'), ('z', 's'),
        ];

        for (from, to) in &substitutions {
            let suggestion = word.replace(*from, &to.to_string());
            if self.offline_rules.spelling_dict.contains_key(&suggestion.to_lowercase()) {
                suggestions.push(suggestion);
            }
        }

        suggestions.truncate(3);
        suggestions
    }

    fn get_ocr_specific_corrections(&self, word: &str) -> Vec<String> {
        let mut corrections = Vec::new();

        // Single character substitutions
        let char_substitutions = [
            ('0', 'O'), ('1', 'l'), ('1', 'I'), ('5', 'S'), ('8', 'B'), ('6', 'G'),
        ];

        for (from, to) in &char_substitutions {
            let corrected = word.replace(*from, &to.to_string());
            if corrected != word && !corrections.contains(&corrected) {
                corrections.push(corrected);
            }
        }

        // Multi-character substitutions
        let string_substitutions = [
            ("rn", "m"), ("cl", "d"), ("vv", "w"), ("nn", "n"), ("li", "h"),
        ];

        for (from, to) in &string_substitutions {
            let corrected = word.replace(from, to);
            if corrected != word && !corrections.contains(&corrected) {
                corrections.push(corrected);
            }
        }

        corrections.truncate(3);
        corrections
    }

    fn capitalize_first_char(&self, text: &str) -> String {
        let mut chars: Vec<char> = text.chars().collect();
        if let Some(first_char) = chars.first_mut() {
            *first_char = first_char.to_uppercase().next().unwrap_or(*first_char);
        }
        chars.into_iter().collect()
    }

    fn check_advanced_grammar_patterns(&self, text: &str) -> Result<Vec<GrammarError>> {
        let mut errors = Vec::new();

        // Check for passive voice patterns
        errors.extend(self.check_passive_voice(text));

        // Check for subject-verb agreement
        errors.extend(self.check_subject_verb_agreement(text));

        // Check for dangling modifiers
        errors.extend(self.check_dangling_modifiers(text));

        // Check for comma splices
        errors.extend(self.check_comma_splices(text));

        // Check for run-on sentences
        errors.extend(self.check_run_on_sentences(text));

        Ok(errors)
    }

    fn check_style_and_readability(&self, text: &str) -> Result<Vec<GrammarError>> {
        let mut errors = Vec::new();

        if self.config.enable_style_checks {
            // Check for wordy phrases
            errors.extend(self.check_wordy_phrases(text));

            // Check for clichés
            errors.extend(self.check_cliches(text));

            // Check for redundant expressions
            errors.extend(self.check_redundant_expressions(text));

            // Check for weak words
            errors.extend(self.check_weak_words(text));
        }

        Ok(errors)
    }

    fn check_punctuation_patterns(&self, text: &str) -> Result<Vec<GrammarError>> {
        let mut errors = Vec::new();

        // Check for missing commas in series
        errors.extend(self.check_oxford_comma(text));

        // Check for incorrect apostrophe usage
        errors.extend(self.check_apostrophe_usage(text));

        // Check for quotation mark consistency
        errors.extend(self.check_quotation_consistency(text));

        // Check for hyphenation issues
        errors.extend(self.check_hyphenation(text));

        Ok(errors)
    }

    pub async fn get_language_stats(&self, text: &str) -> Result<LanguageStats> {
        let words = text.split_whitespace().count();
        let characters = text.chars().count();
        let characters_no_spaces = text.chars().filter(|c| !c.is_whitespace()).count();
        let sentences = text.split(&['.', '!', '?'][..]).filter(|s| !s.trim().is_empty()).count();
        let paragraphs = text.split("\n\n").filter(|p| !p.trim().is_empty()).count();

        Ok(LanguageStats {
            words,
            characters,
            characters_no_spaces,
            sentences,
            paragraphs,
            reading_time_minutes: (words as f64 / 200.0).ceil() as usize, // Assuming 200 WPM
        })
    }
}

impl GrammarService {
    fn check_passive_voice(&self, text: &str) -> Vec<GrammarError> {
        let mut errors = Vec::new();
        let passive_patterns = [
            r"\b(is|are|was|were|being|been)\s+\w+ed\b",
            r"\b(is|are|was|were|being|been)\s+\w+en\b",
        ];

        for pattern in &passive_patterns {
            if let Ok(regex) = regex::Regex::new(pattern) {
                for mat in regex.find_iter(text) {
                    errors.push(GrammarError {
                        message: "Consider using active voice for clearer writing".to_string(),
                        rule_id: "PASSIVE_VOICE".to_string(),
                        category: "Style".to_string(),
                        offset: mat.start(),
                        length: mat.len(),
                        context: self.extract_context(text, mat.start(), mat.len()),
                        suggestions: vec!["Rewrite in active voice".to_string()],
                        severity: "info".to_string(),
                        confidence: 0.6,
                        error_type: ErrorType::Style,
                    });
                }
            }
        }

        errors
    }

    fn check_subject_verb_agreement(&self, text: &str) -> Vec<GrammarError> {
        let mut errors = Vec::new();
        let disagreement_patterns = [
            (r"\b(he|she|it)\s+(are|were)\b", "Use 'is' or 'was' with singular subjects"),
            (r"\b(they|we|you)\s+(is|was)\b", "Use 'are' or 'were' with plural subjects"),
            (r"\b(I)\s+(are|is)\b", "Use 'am' with 'I'"),
        ];

        for (pattern, message) in &disagreement_patterns {
            if let Ok(regex) = regex::Regex::new(pattern) {
                for mat in regex.find_iter(text) {
                    errors.push(GrammarError {
                        message: message.to_string(),
                        rule_id: "SUBJECT_VERB_AGREEMENT".to_string(),
                        category: "Grammar".to_string(),
                        offset: mat.start(),
                        length: mat.len(),
                        context: self.extract_context(text, mat.start(), mat.len()),
                        suggestions: vec!["Check subject-verb agreement".to_string()],
                        severity: "error".to_string(),
                        confidence: 0.8,
                        error_type: ErrorType::Grammar,
                    });
                }
            }
        }

        errors
    }

    fn check_wordy_phrases(&self, text: &str) -> Vec<GrammarError> {
        let mut errors = Vec::new();
        let wordy_replacements = [
            ("in order to", "to"),
            ("due to the fact that", "because"),
            ("at this point in time", "now"),
            ("for the purpose of", "for"),
            ("in the event that", "if"),
            ("with regard to", "about"),
            ("take into consideration", "consider"),
            ("make a decision", "decide"),
        ];

        for (wordy, concise) in &wordy_replacements {
            if let Some(pos) = text.to_lowercase().find(&wordy.to_lowercase()) {
                errors.push(GrammarError {
                    message: format!("Consider using '{}' instead of '{}'", concise, wordy),
                    rule_id: "WORDY_PHRASE".to_string(),
                    category: "Style".to_string(),
                    offset: pos,
                    length: wordy.len(),
                    context: self.extract_context(text, pos, wordy.len()),
                    suggestions: vec![concise.to_string()],
                    severity: "info".to_string(),
                    confidence: 0.7,
                    error_type: ErrorType::Style,
                });
            }
        }

        errors
    }

    fn check_oxford_comma(&self, text: &str) -> Vec<GrammarError> {
        let mut errors = Vec::new();

        // Pattern for series without Oxford comma: "A, B and C" should be "A, B, and C"
        if let Ok(regex) = regex::Regex::new(r"\b\w+,\s+\w+\s+and\s+\w+\b") {
            for mat in regex.find_iter(text) {
                let matched_text = &text[mat.start()..mat.end()];
                if !matched_text.contains(", and") {
                    let suggestion = matched_text.replace(" and ", ", and ");
                    errors.push(GrammarError {
                        message: "Consider using the Oxford comma for clarity".to_string(),
                        rule_id: "OXFORD_COMMA".to_string(),
                        category: "Punctuation".to_string(),
                        offset: mat.start(),
                        length: mat.len(),
                        context: self.extract_context(text, mat.start(), mat.len()),
                        suggestions: vec![suggestion],
                        severity: "info".to_string(),
                        confidence: 0.6,
                        error_type: ErrorType::Punctuation,
                    });
                }
            }
        }

        errors
    }

    // Stub implementations for remaining methods - can be expanded later
    fn check_dangling_modifiers(&self, _text: &str) -> Vec<GrammarError> {
        Vec::new() // TODO: Implement dangling modifier detection
    }

    fn check_comma_splices(&self, _text: &str) -> Vec<GrammarError> {
        Vec::new() // TODO: Implement comma splice detection
    }

    fn check_run_on_sentences(&self, _text: &str) -> Vec<GrammarError> {
        Vec::new() // TODO: Implement run-on sentence detection
    }

    fn check_cliches(&self, _text: &str) -> Vec<GrammarError> {
        Vec::new() // TODO: Implement cliché detection
    }

    fn check_redundant_expressions(&self, _text: &str) -> Vec<GrammarError> {
        Vec::new() // TODO: Implement redundant expression detection
    }

    fn check_weak_words(&self, _text: &str) -> Vec<GrammarError> {
        Vec::new() // TODO: Implement weak word detection
    }

    fn check_apostrophe_usage(&self, _text: &str) -> Vec<GrammarError> {
        Vec::new() // TODO: Implement apostrophe usage checking
    }

    fn check_quotation_consistency(&self, _text: &str) -> Vec<GrammarError> {
        Vec::new() // TODO: Implement quotation consistency checking
    }

    fn check_hyphenation(&self, _text: &str) -> Vec<GrammarError> {
        Vec::new() // TODO: Implement hyphenation checking
    }

    fn check_ocr_specific_errors(&self, text: &str) -> Result<Vec<GrammarError>> {
        let mut errors = Vec::new();

        // Check for common OCR character misrecognitions
        errors.extend(self.check_character_substitutions(text));

        // Check for word boundary issues
        errors.extend(self.check_word_boundary_errors(text));

        // Check for formatting artifacts
        errors.extend(self.check_formatting_artifacts(text));

        // Check for number/letter confusion
        errors.extend(self.check_number_letter_confusion(text));

        // Check for case inconsistencies typical in OCR
        errors.extend(self.check_ocr_case_errors(text));

        Ok(errors)
    }

    fn check_character_substitutions(&self, text: &str) -> Vec<GrammarError> {
        let mut errors = Vec::new();

        // Common OCR character substitution patterns
        let substitution_patterns = [
            (r"\brn\b", "m", "OCR often confuses 'rn' with 'm'"),
            (r"\bcl\b", "d", "OCR often confuses 'cl' with 'd'"),
            (r"\bvv\b", "w", "OCR often confuses 'vv' with 'w'"),
            (r"\bli\b", "h", "OCR often confuses 'li' with 'h'"),
            (r"\b0\b", "O", "OCR often confuses '0' with 'O'"),
            (r"\b1\b", "l", "OCR often confuses '1' with 'l' or 'I'"),
            (r"\b5\b", "S", "OCR often confuses '5' with 'S'"),
            (r"\b8\b", "B", "OCR often confuses '8' with 'B'"),
        ];

        for (pattern, replacement, message) in &substitution_patterns {
            if let Ok(regex) = regex::Regex::new(pattern) {
                for mat in regex.find_iter(text) {
                    errors.push(GrammarError {
                        message: message.to_string(),
                        rule_id: "OCR_CHAR_SUBSTITUTION".to_string(),
                        category: "OCR Error".to_string(),
                        offset: mat.start(),
                        length: mat.len(),
                        context: self.extract_context(text, mat.start(), mat.len()),
                        suggestions: vec![replacement.to_string()],
                        severity: "warning".to_string(),
                        confidence: 0.7,
                        error_type: ErrorType::Other,
                    });
                }
            }
        }

        errors
    }

    fn check_word_boundary_errors(&self, text: &str) -> Vec<GrammarError> {
        let mut errors = Vec::new();

        // Check for missing spaces (common in OCR)
        let merged_word_patterns = [
            (r"\b(the)(and|or|but|with|from|into|onto)\b", "Missing space between words"),
            (r"\b(in)(the|a|an|this|that)\b", "Missing space between words"),
            (r"\b(of)(the|a|an|this|that)\b", "Missing space between words"),
            (r"\b(to)(the|a|an|this|that)\b", "Missing space between words"),
        ];

        for (pattern, message) in &merged_word_patterns {
            if let Ok(regex) = regex::Regex::new(pattern) {
                for mat in regex.find_iter(text) {
                    let matched_text = &text[mat.start()..mat.end()];
                    let suggestion = self.suggest_word_separation(matched_text);

                    errors.push(GrammarError {
                        message: message.to_string(),
                        rule_id: "OCR_WORD_BOUNDARY".to_string(),
                        category: "OCR Error".to_string(),
                        offset: mat.start(),
                        length: mat.len(),
                        context: self.extract_context(text, mat.start(), mat.len()),
                        suggestions: vec![suggestion],
                        severity: "warning".to_string(),
                        confidence: 0.6,
                        error_type: ErrorType::Other,
                    });
                }
            }
        }

        errors
    }

    fn check_formatting_artifacts(&self, text: &str) -> Vec<GrammarError> {
        let mut errors = Vec::new();

        // Check for excessive whitespace (common OCR artifact)
        if let Ok(regex) = regex::Regex::new(r"\s{3,}") {
            for mat in regex.find_iter(text) {
                errors.push(GrammarError {
                    message: "Excessive whitespace detected (possible OCR artifact)".to_string(),
                    rule_id: "OCR_EXCESSIVE_WHITESPACE".to_string(),
                    category: "Formatting".to_string(),
                    offset: mat.start(),
                    length: mat.len(),
                    context: self.extract_context(text, mat.start(), mat.len()),
                    suggestions: vec![" ".to_string()],
                    severity: "info".to_string(),
                    confidence: 0.8,
                    error_type: ErrorType::Style,
                });
            }
        }

        // Check for random line breaks in middle of words
        if let Ok(regex) = regex::Regex::new(r"\b\w+\n\w+\b") {
            for mat in regex.find_iter(text) {
                let matched_text = &text[mat.start()..mat.end()];
                let suggestion = matched_text.replace('\n', "");

                errors.push(GrammarError {
                    message: "Unexpected line break in word (possible OCR artifact)".to_string(),
                    rule_id: "OCR_WORD_BREAK".to_string(),
                    category: "Formatting".to_string(),
                    offset: mat.start(),
                    length: mat.len(),
                    context: self.extract_context(text, mat.start(), mat.len()),
                    suggestions: vec![suggestion],
                    severity: "warning".to_string(),
                    confidence: 0.7,
                    error_type: ErrorType::Other,
                });
            }
        }

        errors
    }

    fn check_number_letter_confusion(&self, text: &str) -> Vec<GrammarError> {
        let mut errors = Vec::new();

        // Common number/letter confusions in context
        let confusion_patterns = [
            (r"\b1st\b", "1st", "Check if this should be 'first'"),
            (r"\b2nd\b", "2nd", "Check if this should be 'second'"),
            (r"\b3rd\b", "3rd", "Check if this should be 'third'"),
            (r"\bI0\b", "10", "OCR often confuses 'I' and '1' with '0'"),
            (r"\bO0\b", "00", "OCR often confuses 'O' with '0'"),
        ];

        for (pattern, _replacement, message) in &confusion_patterns {
            if let Ok(regex) = regex::Regex::new(pattern) {
                for mat in regex.find_iter(text) {
                    errors.push(GrammarError {
                        message: message.to_string(),
                        rule_id: "OCR_NUMBER_LETTER_CONFUSION".to_string(),
                        category: "OCR Error".to_string(),
                        offset: mat.start(),
                        length: mat.len(),
                        context: self.extract_context(text, mat.start(), mat.len()),
                        suggestions: vec!["Review this text".to_string()],
                        severity: "info".to_string(),
                        confidence: 0.5,
                        error_type: ErrorType::Other,
                    });
                }
            }
        }

        errors
    }

    fn check_ocr_case_errors(&self, text: &str) -> Vec<GrammarError> {
        let mut errors = Vec::new();

        // Check for inconsistent capitalization patterns typical in OCR
        if let Ok(regex) = regex::Regex::new(r"\b[a-z][A-Z]+[a-z]+\b") {
            for mat in regex.find_iter(text) {
                let matched_text = &text[mat.start()..mat.end()];
                let suggestion = matched_text.to_lowercase();

                errors.push(GrammarError {
                    message: "Inconsistent capitalization (possible OCR error)".to_string(),
                    rule_id: "OCR_CASE_INCONSISTENCY".to_string(),
                    category: "OCR Error".to_string(),
                    offset: mat.start(),
                    length: mat.len(),
                    context: self.extract_context(text, mat.start(), mat.len()),
                    suggestions: vec![suggestion],
                    severity: "info".to_string(),
                    confidence: 0.6,
                    error_type: ErrorType::Style,
                });
            }
        }

        errors
    }

    fn suggest_word_separation(&self, merged_text: &str) -> String {
        // Simple heuristic to suggest word separation
        // This could be enhanced with a more sophisticated algorithm
        let common_separations = [
            ("theand", "the and"),
            ("inthe", "in the"),
            ("ofthe", "of the"),
            ("tothe", "to the"),
            ("andthe", "and the"),
        ];

        for (merged, separated) in &common_separations {
            if merged_text.to_lowercase().contains(merged) {
                return merged_text.to_lowercase().replace(merged, separated);
            }
        }

        // Fallback: try to insert space in the middle
        let mid = merged_text.len() / 2;
        format!("{} {}", &merged_text[..mid], &merged_text[mid..])
    }
}

pub struct OfflineGrammarChecker {
    pub spelling_dict: HashMap<String, bool>,
    pub common_errors: HashMap<String, String>,
    pub ocr_corrections: HashMap<String, String>,
    pub domain_dictionary: HashMap<String, bool>,
}

impl OfflineGrammarChecker {
    pub fn new() -> Self {
        let mut checker = Self {
            spelling_dict: HashMap::new(),
            common_errors: HashMap::new(),
            ocr_corrections: HashMap::new(),
            domain_dictionary: HashMap::new(),
        };

        checker.initialize_rules();
        checker
    }

    fn initialize_rules(&mut self) {
        // Add common spelling corrections
        self.common_errors.insert("teh".to_string(), "the".to_string());
        self.common_errors.insert("recieve".to_string(), "receive".to_string());
        self.common_errors.insert("seperate".to_string(), "separate".to_string());
        self.common_errors.insert("definately".to_string(), "definitely".to_string());
        self.common_errors.insert("occured".to_string(), "occurred".to_string());
        self.common_errors.insert("accomodate".to_string(), "accommodate".to_string());
        self.common_errors.insert("neccessary".to_string(), "necessary".to_string());
        self.common_errors.insert("embarass".to_string(), "embarrass".to_string());
        self.common_errors.insert("existance".to_string(), "existence".to_string());
        self.common_errors.insert("maintainance".to_string(), "maintenance".to_string());

        // Add OCR-specific corrections
        self.initialize_ocr_corrections();

        // Add domain-specific terms
        self.initialize_domain_dictionary();

        // Add basic dictionary words (in a real implementation, this would be loaded from a file)
        let common_words = vec![
            "the", "be", "to", "of", "and", "a", "in", "that", "have", "i", "it", "for", "not", "on", "with", "he", "as", "you", "do", "at",
            "this", "but", "his", "by", "from", "they", "we", "say", "her", "she", "or", "an", "will", "my", "one", "all", "would", "there", "their",
        ];

        for word in common_words {
            self.spelling_dict.insert(word.to_string(), true);
        }
    }

    fn initialize_ocr_corrections(&mut self) {
        // Common OCR character misrecognitions
        let ocr_errors = [
            ("rn", "m"), ("cl", "d"), ("vv", "w"), ("nn", "n"), ("li", "h"),
            ("0", "O"), ("1", "l"), ("1", "I"), ("5", "S"), ("8", "B"), ("6", "G"),
            ("rnore", "more"), ("tlie", "the"), ("witli", "with"), ("tliis", "this"),
            ("wlien", "when"), ("wliere", "where"), ("wliich", "which"),
        ];

        for (error, correction) in &ocr_errors {
            self.ocr_corrections.insert(error.to_string(), correction.to_string());
        }
    }

    fn initialize_domain_dictionary(&mut self) {
        // Add technical and domain-specific terms that might not be in standard dictionaries
        let domain_terms = [
            "API", "JSON", "XML", "HTTP", "HTTPS", "URL", "URI", "SQL", "NoSQL",
            "JavaScript", "TypeScript", "Python", "Rust", "OCR", "AI", "ML",
            "frontend", "backend", "fullstack", "DevOps", "CI/CD", "SaaS", "PaaS",
        ];

        for term in &domain_terms {
            self.domain_dictionary.insert(term.to_lowercase(), true);
            self.domain_dictionary.insert(term.to_string(), true);
        }
    }

    pub fn check_text(&self, text: &str) -> Vec<GrammarError> {
        let mut errors = Vec::new();

        // Check for spelling errors
        errors.extend(self.check_spelling(text));

        // Check for basic grammar patterns
        errors.extend(self.check_basic_grammar(text));

        // Check for punctuation issues
        errors.extend(self.check_punctuation(text));

        errors
    }

    fn check_spelling(&self, text: &str) -> Vec<GrammarError> {
        let mut errors = Vec::new();
        let words: Vec<&str> = text.split_whitespace().collect();
        let mut offset = 0;

        for word in words {
            let clean_word = word.trim_matches(|c: char| !c.is_alphabetic());
            let lower_word = clean_word.to_lowercase();

            if !clean_word.is_empty() && clean_word.len() > 1 {
                let mut suggestions = Vec::new();
                let mut is_misspelled = false;

                // Check common errors first
                if let Some(correction) = self.common_errors.get(&lower_word) {
                    suggestions.push(correction.clone());
                    is_misspelled = true;
                }

                // Check OCR-specific corrections
                if let Some(correction) = self.ocr_corrections.get(&lower_word) {
                    if !suggestions.contains(correction) {
                        suggestions.push(correction.clone());
                    }
                    is_misspelled = true;
                }

                // Check if word is in any of our dictionaries
                let in_dict = self.spelling_dict.contains_key(&lower_word) ||
                             self.domain_dictionary.contains_key(&lower_word) ||
                             self.domain_dictionary.contains_key(clean_word);

                // If not in dictionary and no specific corrections, it might be misspelled
                if !in_dict && suggestions.is_empty() {
                    // Generate suggestions using edit distance and common patterns
                    suggestions.extend(self.generate_spelling_suggestions(&lower_word));
                    if !suggestions.is_empty() {
                        is_misspelled = true;
                    }
                }

                if is_misspelled {
                    errors.push(GrammarError {
                        message: format!("Possible spelling mistake: '{}'", clean_word),
                        rule_id: "SPELLING_MISTAKE".to_string(),
                        category: "Spelling".to_string(),
                        offset,
                        length: word.len(),
                        context: self.extract_context_simple(text, offset, word.len()),
                        suggestions,
                        severity: "error".to_string(),
                        confidence: if self.common_errors.contains_key(&lower_word) { 0.9 } else { 0.7 },
                        error_type: ErrorType::Spelling,
                    });
                }
            }

            offset += word.len() + 1; // +1 for space
        }

        errors
    }

    fn generate_spelling_suggestions(&self, word: &str) -> Vec<String> {
        let mut suggestions = Vec::new();

        // Check for common letter substitutions
        let substitutions = [
            ('a', 'e'), ('e', 'a'), ('i', 'e'), ('e', 'i'), ('o', 'u'), ('u', 'o'),
            ('c', 'k'), ('k', 'c'), ('s', 'z'), ('z', 's'),
        ];

        for (from, to) in &substitutions {
            let suggestion = word.replace(*from, &to.to_string());
            if self.spelling_dict.contains_key(&suggestion) {
                suggestions.push(suggestion);
            }
        }

        // Check for missing or extra letters
        if word.len() > 1 {
            // Try removing each character
            for i in 0..word.len() {
                let mut chars: Vec<char> = word.chars().collect();
                chars.remove(i);
                let suggestion: String = chars.into_iter().collect();
                if self.spelling_dict.contains_key(&suggestion) {
                    suggestions.push(suggestion);
                }
            }
        }

        suggestions.truncate(3); // Limit suggestions
        suggestions
    }

    fn extract_context_simple(&self, text: &str, offset: usize, length: usize) -> String {
        let context_size = 30;
        let start = offset.saturating_sub(context_size);
        let end = (offset + length + context_size).min(text.len());
        text[start..end].to_string()
    }

    fn check_basic_grammar(&self, text: &str) -> Vec<GrammarError> {
        let mut errors = Vec::new();

        // Check for double spaces
        if let Some(pos) = text.find("  ") {
            errors.push(GrammarError {
                message: "Multiple consecutive spaces found".to_string(),
                rule_id: "DOUBLE_SPACE".to_string(),
                category: "Whitespace".to_string(),
                offset: pos,
                length: 2,
                context: text.to_string(),
                suggestions: vec![" ".to_string()],
                severity: "info".to_string(),
                confidence: 0.9,
                error_type: ErrorType::Style,
            });
        }

        // Check for sentence starting with lowercase (basic check)
        let sentences: Vec<&str> = text.split(&['.', '!', '?'][..]).collect();
        let mut offset = 0;

        for (i, sentence) in sentences.iter().enumerate() {
            let trimmed = sentence.trim();
            if !trimmed.is_empty() && i > 0 {
                if let Some(first_char) = trimmed.chars().next() {
                    if first_char.is_lowercase() {
                        errors.push(GrammarError {
                            message: "Sentence should start with a capital letter".to_string(),
                            rule_id: "SENTENCE_CAPITALIZATION".to_string(),
                            category: "Grammar".to_string(),
                            offset: offset + sentence.len() - trimmed.len(),
                            length: 1,
                            context: text.to_string(),
                            suggestions: vec![first_char.to_uppercase().to_string()],
                            severity: "warning".to_string(),
                            confidence: 0.7,
                            error_type: ErrorType::Grammar,
                        });
                    }
                }
            }
            offset += sentence.len() + 1; // +1 for punctuation
        }

        errors
    }

    fn check_punctuation(&self, text: &str) -> Vec<GrammarError> {
        let mut errors = Vec::new();

        // Check for space before punctuation
        let punctuation_chars = [',', '.', '!', '?', ';', ':'];
        for &punct in &punctuation_chars {
            let pattern = format!(" {}", punct);
            if let Some(pos) = text.find(&pattern) {
                errors.push(GrammarError {
                    message: format!("Unnecessary space before '{}'", punct),
                    rule_id: "SPACE_BEFORE_PUNCTUATION".to_string(),
                    category: "Punctuation".to_string(),
                    offset: pos,
                    length: 2,
                    context: text.to_string(),
                    suggestions: vec![punct.to_string()],
                    severity: "warning".to_string(),
                    confidence: 0.8,
                    error_type: ErrorType::Punctuation,
                });
            }
        }

        errors
    }
}


