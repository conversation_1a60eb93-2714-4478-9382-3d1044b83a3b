#[cfg(test)]
mod tests {
    use crate::services::{GrammarService, GrammarConfig, GrammarProvider};

    #[tokio::test]
    async fn test_harper_basic_functionality() {
        let mut config = GrammarConfig::default();
        config.provider = GrammarProvider::Harper;
        
        let service = GrammarService::with_config(config);
        
        // Test with text that has obvious errors
        let test_text = "This is a test sentance with some erors.";
        
        let result = service.check_text(test_text, false).await;
        
        assert!(result.is_ok());
        let grammar_result = result.unwrap();
        
        println!("Original text: {}", grammar_result.original_text);
        println!("Errors found: {}", grammar_result.error_count);
        
        for (i, error) in grammar_result.errors.iter().enumerate() {
            println!("Error {}: {} - {}", i + 1, error.message, error.rule_id);
            println!("  Suggestions: {:?}", error.suggestions);
        }
        
        // <PERSON> should find at least some errors in this text
        assert!(grammar_result.error_count > 0, "<PERSON> should detect errors in the test text");
    }

    #[tokio::test]
    async fn test_harper_with_correct_text() {
        let mut config = GrammarConfig::default();
        config.provider = GrammarProvider::Harper;
        
        let service = GrammarService::with_config(config);
        
        // Test with correct text
        let test_text = "This is a well-written sentence with proper grammar.";
        
        let result = service.check_text(test_text, false).await;
        
        assert!(result.is_ok());
        let grammar_result = result.unwrap();
        
        println!("Correct text test - Errors found: {}", grammar_result.error_count);
        
        // Should have fewer or no errors
        assert!(grammar_result.error_count <= 2, "Harper should find few or no errors in correct text");
    }

    #[tokio::test]
    async fn test_harper_auto_correction() {
        let mut config = GrammarConfig::default();
        config.provider = GrammarProvider::Harper;
        config.auto_apply_high_confidence = true;
        config.auto_apply_threshold = 0.8;
        
        let service = GrammarService::with_config(config);
        
        // Test with text that has obvious errors
        let test_text = "This is a test sentance with some erors.";
        
        let result = service.check_text(test_text, true).await;
        
        assert!(result.is_ok());
        let grammar_result = result.unwrap();
        
        println!("Original: {}", grammar_result.original_text);
        println!("Corrected: {}", grammar_result.corrected_text);
        
        // The corrected text should be different from the original
        if grammar_result.error_count > 0 {
            // Only assert if errors were found
            assert_ne!(grammar_result.original_text, grammar_result.corrected_text, 
                      "Auto-correction should modify the text when errors are found");
        }
    }

    #[tokio::test]
    async fn test_harper_ocr_specific_errors() {
        let mut config = GrammarConfig::default();
        config.provider = GrammarProvider::Harper;
        
        let service = GrammarService::with_config(config);
        
        // Test with OCR-like errors
        let test_text = "The quick brown fox jurnps over the lazy dog. This sentance has sorne OCR erors.";
        
        let result = service.check_text(test_text, false).await;
        
        assert!(result.is_ok());
        let grammar_result = result.unwrap();
        
        println!("OCR text test - Errors found: {}", grammar_result.error_count);
        
        for error in &grammar_result.errors {
            println!("Error: {} ({})", error.message, error.category);
        }
        
        // Should detect some errors
        assert!(grammar_result.error_count > 0, "Harper should detect OCR-like errors");
    }

    #[tokio::test]
    async fn test_language_statistics() {
        let service = GrammarService::new();
        
        let test_text = "This is a test sentence. It has multiple sentences and words. This helps test the statistics functionality.";
        
        let result = service.get_language_stats(test_text).await;
        
        assert!(result.is_ok());
        let stats = result.unwrap();
        
        println!("Language Statistics:");
        println!("  Words: {}", stats.words);
        println!("  Characters: {}", stats.characters);
        println!("  Characters (no spaces): {}", stats.characters_no_spaces);
        println!("  Sentences: {}", stats.sentences);
        println!("  Paragraphs: {}", stats.paragraphs);
        println!("  Reading time: {} minutes", stats.reading_time_minutes);
        
        assert!(stats.words > 0);
        assert!(stats.characters > 0);
        assert!(stats.sentences > 0);
    }
}
